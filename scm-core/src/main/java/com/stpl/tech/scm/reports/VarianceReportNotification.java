/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.reports;

import java.text.SimpleDateFormat;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

public class VarianceReportNotification extends EmailNotification {

	private VarianceReport output;
	String[] toEmails;

	public VarianceReportNotification(VarianceReport output, String[] toEmails) {
		this.output = output;
		setToEmails(toEmails);
	}

	@Override
	public String[] getToEmails() {
		return toEmails;
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		return (SCMUtil.isDev(getEnvironmentType()) ? "DEV : " : "") + "Variance Report for : " + output.getUnitName() + " for "
				+ new SimpleDateFormat("yyyy-MM-dd").format(output.getBusinessDate());
	}

	@Override
	public String body() throws EmailGenerationException {
		return "<html><p>" + " Variance Report for : " + output.getUnitName() + " for "
				+ new SimpleDateFormat("yyyy-MM-dd").format(output.getBusinessDate()) + "</p></html>";
	}

	@Override
	public EnvType getEnvironmentType() {
		return output.getEnv();
	}


	public void setToEmails(String[] toEmails){
        this.toEmails = SCMUtil.isDev(getEnvironmentType()) ? new String[] { "<EMAIL>" } : toEmails;
    }
}

package com.stpl.tech.scm.reports;

import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.scm.core.exception.StockTakeException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.FaStockEventExtraScannedItems;
import com.stpl.tech.scm.data.model.StockEventAssetMappingDefinitionData;
import com.stpl.tech.scm.data.model.StockEventDefinitionData;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.scm.reports.modal.VarianceModal;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.subtlelib.poi.api.row.RowContext;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class FAStockTakeReport extends ReportData implements FixedAssetStockTakeReport{
    private final static Logger LOG = LoggerFactory.getLogger(FAStockTakeReport.class);
    private WorkbookContext workbookCtx;
    private VarianceType varianceType = VarianceType.FIXED_ASSETS;
    private int unitId;
    private String unitName;
    private int eventId;
    private String eventSubtype;
    private String filePath;
    private Date businessDate;
    private EnvType envType;
    private String toEmail;
    private boolean generated = false;
    private String fileName;
    private String mimeType = AppConstants.EXCEL_MIME_TYPE;
    private SCMAssetManagementDao scmAssetManagementDao;
    private EnvProperties props;

    public FAStockTakeReport(WorkbookContextFactory ctxFactory, int unitId, String unitName,
                             int eventId, String eventSubtype, String basePath, Date businessDate, EnvType envType, String toEmail,
                             SCMAssetManagementDao scmAssetManagementDao, EnvProperties props) {
        this.workbookCtx = ctxFactory.createWorkbook();
        this.unitId = unitId;
        this.unitName = unitName;
        this.eventId = eventId;
        this.eventSubtype = eventSubtype;
        this.filePath = basePath + "variance_reports" + File.separator;
        this.businessDate = businessDate;
        this.envType = envType;
        this.toEmail = toEmail;
        this.fileName = this.varianceType.toString() + " Stock Take Report " + SCMUtil.getCurrentTimeISTStringWithNoColons()+ "_" + this.unitName + "_"
                + this.unitId;
        this.scmAssetManagementDao = scmAssetManagementDao;
        this.props = props;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void renderSummary(){
        StockTakeReportSummaryObject data  = scmAssetManagementDao.getSummaryByEventId(eventId, envType);
        List<StockEventDefinitionData> list = scmAssetManagementDao.getParentOrChildEvent(null, eventId,null);
        if(list != null && !list.isEmpty()){
            data.setScannedAssets(0);
            data.setNotScannedAssets(0);
            data.setProcurementNotScanned(0);
            for(StockEventDefinitionData event: list){
                StockTakeReportSummaryObject tempData  = scmAssetManagementDao.getSummaryByEventId(event.getEventId(), envType);
                data.setScannedAssets(data.getScannedAssets().doubleValue() + tempData.getScannedAssets().doubleValue());
                data.setNotScannedAssets(data.getNotScannedAssets().doubleValue() + tempData.getNotScannedAssets().doubleValue());
                data.setProcurementNotScanned(data.getProcurementNotScanned().doubleValue() + tempData.getProcurementNotScanned().doubleValue());
//                data.setExcessViaScan(data.getExcessViaScan().doubleValue() + tempData.getExcessViaScan().doubleValue());
//                data.setExcessViaNoSticker(data.getExcessViaNoSticker().doubleValue() + tempData.getExcessViaNoSticker().doubleValue());
//                data.setProcurementExcess(data.getProcurementExcess().doubleValue() + tempData.getProcurementExcess().doubleValue());
            }
        }

        if (data == null) {
            LOG.info("Stock Take Returned No Summary for Event: " + eventId);
            return ;
        }

        SheetContext sheetCtx = workbookCtx.createSheet("Summary");

        sheetCtx.nextRow().mergeCells(2).setTextStyle(SCMUtil.getHeaderStyle(workbookCtx)).text("Fixed Asset Stock Take Report"); // heading
        sheetCtx.nextRow().text("Event ID").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .number(data.getEventId()).setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Event Type").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getEventType()).setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Event Status").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getEventStatus()).setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Event Creation time").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text(data.getEventCreationTime().toString()).setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Event Updation time").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text( data.getEventUpdationTime() != null ? data.getEventUpdationTime().toString():data.getEventCreationTime().toString()).setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Unit ID").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .number(data.getUnitId()).setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Unit Name").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getUnitName()).setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Unit Type").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getUnitType()).setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Initiaited By").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getInitiatedBy()).setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Recipient").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getRecipient() != null ?data.getRecipient():"").setColumnWidth(SCMUtil.COLUMN_WIDTH);
        sheetCtx.nextRow().text("Last Stock Take Date").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text(data.getLastStockTakeDate() != null ?data.getLastStockTakeDate().toString():data.getEventCreationTime().toString()).setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx));
        sheetCtx.nextRow().text("Last Stock Take By").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getLastStockTakeBy() != null ?data.getLastStockTakeBy().toString():"").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx));
        sheetCtx.nextRow().text("Total Assets in System").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .number(data.getTotalAssetsinSystem()).setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx));
        sheetCtx.nextRow().text("Scanned Succesfully").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .number(data.getScannedAssets()).setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx));
        sheetCtx.nextRow().text("Not Scanned").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .number(data.getNotScannedAssets()).setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx));
        sheetCtx.nextRow().text("Procurement Value of Not Scanned").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .number(data.getProcurementNotScanned()).setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx));
        sheetCtx.nextRow().text("Excess Items (Via scanning)").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getExcessViaScan()!= null ?data.getExcessViaScan().toString():"").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx));
        sheetCtx.nextRow().text("Excess Items (Sticker not found)").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getExcessViaNoSticker()!= null ?data.getExcessViaNoSticker().toString():"").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx));
        sheetCtx.nextRow().text("Procurement Value of Excess (with tags)").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                .text(data.getProcurementExcess() != null ?data.getProcurementExcess().toString():"").setColumnWidth(SCMUtil.COLUMN_WIDTH).setTextStyle(SCMUtil.getCellStyle(workbookCtx));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void renderFoundExtra() {
        List<ExcessAssetReportObject> data= scmAssetManagementDao.getExcessAssetsByEventId(eventId, envType);
        List<StockEventDefinitionData> list = scmAssetManagementDao.getParentOrChildEvent(null, eventId,null);
        if(list != null && !list.isEmpty()){
            for(StockEventDefinitionData event: list){
                List<ExcessAssetReportObject> tempData  = scmAssetManagementDao.getExcessAssetsByEventId(event.getEventId(), envType);
                data.addAll(tempData);
            }
        }

        if (data == null || data.isEmpty()) {
            LOG.info("Stock Take Returned No Extra Asset for Event: " + eventId);
            return ;
        }

        SheetContext sheetCtx = workbookCtx.createSheet("Excess Stock Found");

//        sheetCtx.nextRow().mergeCells(11).text(this.unitName); // heading
        sheetCtx.nextRow().setTextStyle(SCMUtil.getHeaderStyle(workbookCtx))
                .text("Excess Type").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Name").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Tag").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Status").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Sub Category").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Product Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("SKU Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Unit Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Unit Name").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Creation Date").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Last Transfer Date").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Scan Time").setColumnWidth(SCMUtil.COLUMN_WIDTH);

        for (ExcessAssetReportObject obj : data) {
            sheetCtx.nextRow().setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                    .text(obj.getExcessType())
                    .number(obj.getAssetId())
                    .text(obj.getAssetName())
                    .text(obj.getAssetTag())
                    .text(obj.getAssetStatus())
                    .text(obj.getSubCategory())
                    .number(obj.getProductId())
                    .number(obj.getSkuId())
                    .number(obj.getUnitId())
                    .text(obj.getUnitName())
                    .text(obj.getCreatedDate() != null ?obj.getCreatedDate().toString():"")
                    .text(obj.getLastTransferDate() != null ?obj.getLastTransferDate().toString():"")
                    .text(obj.getScanTime() != null ?obj.getScanTime().toString():"");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void renderScanned() {
        List<AssetScanReportObject> data = scmAssetManagementDao.getScannedAssetsByEventId(eventId, envType);
        List<StockEventDefinitionData> list = scmAssetManagementDao.getParentOrChildEvent(null, eventId,null);
        if(list != null && !list.isEmpty()){
            for(StockEventDefinitionData event: list){
                List<AssetScanReportObject> tempData  = scmAssetManagementDao.getScannedAssetsByEventId(event.getEventId(), envType);
                data.addAll(tempData);
            }
        }
        if (data == null || data.isEmpty()) {
            LOG.info("Stock Take Returned No Asset for Event: " + eventId);
            return ;
        }


        SheetContext sheetCtx = workbookCtx.createSheet("Stock Take");

//        sheetCtx.nextRow().mergeCells(11).text(this.unitName); // heading
        sheetCtx.nextRow().setTextStyle(SCMUtil.getHeaderStyle(workbookCtx))
                .text("Asset Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Name").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Tag").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Status").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Sub Category").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Product Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("SKU Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Creation Date").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Last Transfer Date").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Scan Time").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Last Stock Take").setColumnWidth(SCMUtil.COLUMN_WIDTH);

        for (AssetScanReportObject obj : data) {
            sheetCtx.nextRow().setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                    .number(obj.getAssetId())
                    .text(obj.getAssetName())
                    .text(obj.getAssetTag())
                    .text(obj.getAssetStatus())
                    .text(obj.getSubCategory())
                    .number(obj.getProductId())
                    .number(obj.getSkuId())
                    .text(obj.getCreatedDate() != null ?obj.getCreatedDate().toString():"")
                    .text(obj.getLastTransferDate() != null ?obj.getLastTransferDate().toString():"")
                    .text("")
                    .text("");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void renderNotFound() {
        List<AssetScanReportObject> data = scmAssetManagementDao.getLostAssetsByEventId(eventId, envType);
        List<StockEventDefinitionData> list = scmAssetManagementDao.getParentOrChildEvent(null, eventId,null);
        if(list != null && !list.isEmpty()){
            data.clear();
            for(StockEventDefinitionData event: list){
                List<AssetScanReportObject> tempData  = scmAssetManagementDao.getLostAssetsByEventId(event.getEventId(), envType);
                data.addAll(tempData);
            }
        }
        if (data == null || data.isEmpty()) {
            LOG.info("Stock Take Returned No Lost Asset for Event: " + eventId);
            return ;
        }


        SheetContext sheetCtx = workbookCtx.createSheet("Stock Not Found");

//        sheetCtx.nextRow().mergeCells(12).text(this.unitName); // heading
        sheetCtx.nextRow().setTextStyle(SCMUtil.getHeaderStyle(workbookCtx))
                .text("Asset Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Name").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Tag").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Asset Status").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Sub Category").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Procurement Cost").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Product Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("SKU Id").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Creation Date").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Last Transfer Date").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Scan Time").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Last Stock Take").setColumnWidth(SCMUtil.COLUMN_WIDTH);

        for (AssetScanReportObject obj : data) {
            sheetCtx.nextRow().setTextStyle(SCMUtil.getCellStyle(workbookCtx))
                    .number(obj.getAssetId())
                    .text(obj.getAssetName())
                    .text(obj.getAssetTag())
                    .text(obj.getAssetStatus())
                    .text(obj.getSubCategory())
                    .number(obj.getProcurementCost())
                    .number(obj.getProductId())
                    .number(obj.getSkuId())
                    .text(obj.getCreatedDate() != null ?obj.getCreatedDate().toString():"")
                    .text(obj.getLastTransferDate() != null ?obj.getLastTransferDate().toString():"")
                    .text("")
                    .text("");
        }
    }

    @Override
    public void generateReport(String filePath, byte[] content) throws IOException {
        String writtenPath = SCMUtil.write(content, filePath, this.unitName, this.varianceType.toString(),
                this.fileName, LOG);
        if (writtenPath != null) {
            this.filePath = writtenPath;
            this.generated = true;
        }
    }

    @Override
    public int getEventId() {
        return eventId;
    }

    @Override
    public String getEventSubtype() {
        return eventSubtype;
    }

    @Override
    public String getEmailId() {
        return toEmail;
    }

    @Override
    public String getUnitName() {
        return unitName;
    }

    @Override
    public boolean isGenerated() {
        return generated;
    }

    @Override
    public String getFileName() {
        return fileName;
    }

    @Override
    public String getMimeType() {
        return mimeType;
    }

    @Override
    public WorkbookContext getWorkbook() {
        return workbookCtx;
    }

    @Override
    public String getFilePath() {
        return filePath;
    }

    @Override
    public Date getBusinessDate() {
        return businessDate;
    }

    @Override
    public EnvType getEnv() {
        return envType;
    }
}

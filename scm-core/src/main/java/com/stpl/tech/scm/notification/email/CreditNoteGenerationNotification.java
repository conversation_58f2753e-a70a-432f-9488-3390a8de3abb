package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.CreditNoteGenerationTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class CreditNoteGenerationNotification extends EmailNotification {

    private EnvType envType;
    private String vendor;
    private CreditNoteGenerationTemplate template;

    public CreditNoteGenerationNotification(EnvType envType, CreditNoteGenerationTemplate template , String vendor) {
        this.envType = envType;
        this.template = template;
        this.vendor = vendor;
    }

    @Override
    public String[] getToEmails() {
        if (SCMUtil.isDev(envType)) {
            return new String[] { "<EMAIL>" };
        } else {
            return new String[] { "<EMAIL>" };
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String msg  = "Credit Note Generated For Vendor : " + vendor;
        String subject = msg + " On " +
                AppUtils.getTimeISTString(AppUtils.getCurrentTimestamp());
        if (AppUtils.isDev(envType)) {
            subject = "[DEV] : " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
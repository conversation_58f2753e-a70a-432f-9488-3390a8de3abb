package com.stpl.tech.scm.core.util;

import com.amazonaws.util.CollectionUtils;
import com.amazonaws.util.StringUtils;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderVendorGRMappingData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import lombok.RequiredArgsConstructor;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class FlowDeciderService {

    private final EnvProperties envProperties;

    private Date getNewBudgetFlowDeployedDate() {
        String deployedDate = envProperties.getProductWiseBudgetDeductionDeploymentDate();
        if(StringUtils.isNullOrEmpty(deployedDate)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.parse(deployedDate);
        } catch (ParseException e) {
            throw new IllegalArgumentException("Invalid deployment date format: " + deployedDate, e);
        }
    }

    @Deprecated
    public boolean shouldUseNewBudgetFlow(PurchaseOrderData po) {
        if(po == null) {
            return true;
        }
        Date poGenerationDate = po.getGenerationTime();

        if(poGenerationDate == null) {
            return true;
        }
        Date deployedDate = getNewBudgetFlowDeployedDate();
        if(deployedDate == null) {
            return true;
        }

        // Compare: if PO generationTime >= deployedDate → new flow
        return !poGenerationDate.before(deployedDate);
    }

    @Deprecated
    public boolean shouldUseNewBudgetFlow(VendorGoodsReceivedData vendorGR) {
        if(vendorGR == null) {
            return true;
        }
        List<PurchaseOrderVendorGRMappingData> poMappingList = vendorGR.getPoMappingList();

        if(CollectionUtils.isNullOrEmpty(poMappingList)) {
            return true;
        }

        PurchaseOrderData po = poMappingList.get(0).getPurchaseOrderData();
        return shouldUseNewBudgetFlow(po);
    }

}

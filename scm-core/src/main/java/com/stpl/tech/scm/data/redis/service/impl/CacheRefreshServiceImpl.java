package com.stpl.tech.scm.data.redis.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.impl.SCMMetadataServiceImpl;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.redis.service.CacheRefreshService;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.VendorDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class CacheRefreshServiceImpl implements CacheRefreshService {

    @Autowired
    private SCMMetadataServiceImpl scmMetadataService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public Map<Integer, ProductDefinition> reloadProductCache() {
        List<ProductDefinitionData> productList = scmMetadataService.getAllProducts();
        return refreshProductDefinitionCache(productList);
    }

    private Map<Integer, ProductDefinition> refreshProductDefinitionCache(List<ProductDefinitionData> allProducts) {
        Map<Integer, String> employees = masterDataCache.getEmployees();
        Map<Integer, ProductPriceData> productPrices = scmCache.getProductPrices();
        Map<Integer, ProductDefinition> productDefinitions = new HashMap<>();
        for (ProductDefinitionData data: allProducts) {
            IdCodeName createdBy = SCMUtil.generateIdCodeName(
                    data.getCreatedBy(), "", employees.get(data.getCreatedBy()));
            ProductDefinition pd = SCMDataConverter.convert(data, createdBy);
            productDefinitions.put(pd.getProductId(), pd);
            productPrices.put(pd.getProductId(), SCMDataConverter.convertToPrice(pd));
        }
        return productDefinitions;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public Map<Integer, SkuDefinition> reloadSkuCache() {
        List<SkuDefinitionData> allSkus = scmMetadataService.getAllSkus();
        return refreshSkuDefinitionCache(allSkus);
    }

    private Map<Integer, SkuDefinition> refreshSkuDefinitionCache(List<SkuDefinitionData> skuList) {
        Map<Integer, String> employees = masterDataCache.getEmployees();
        Map<Integer, SkuDefinition> skuDefinitions = new HashMap<>();
        skuList.forEach(data -> {
            IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "",
                    employees.get(data.getCreatedBy()));
            skuDefinitions.put(data.getSkuId(), SCMDataConverter.convert(data, createdBy) );
        });
        return skuDefinitions;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public Map<Integer, VendorDetail> reloadVendorCache() {
        List<VendorDetailData> allVendors = scmMetadataService.getAllVendors();
        return refreshVendorDetailCache(allVendors);
    }

    private Map<Integer, VendorDetail> refreshVendorDetailCache(List<VendorDetailData> vendorList) {
        if (CollectionUtils.isEmpty(vendorList)) {
            return new HashMap<>();
        }
        Map<Integer, VendorDetail> vendorDetails = new HashMap<>();
        Map<Integer, String> employees = masterDataCache.getEmployees();
        vendorList.forEach(data -> {
            try {
                String updatedBy = employees.get(data.getUpdatedBy());
                String requestedBy = employees.get(data.getRequestedBy());
                VendorDetail detail = SCMDataConverter.convertVendor(data, updatedBy, requestedBy);
                if (Objects.nonNull(data.getLastUnBlockedBy())) {
                    detail.setLastUnBlockedBy(SCMUtil.getCreatedBy(employees.get(data.getLastUnBlockedBy()), data.getLastUnBlockedBy()));
                }
                if (Objects.nonNull(data.getLastBlockedBy())) {
                    detail.setLastBlockedBy(SCMUtil.getCreatedBy(employees.get(data.getLastBlockedBy()), data.getLastBlockedBy()));
                }
                vendorDetails.put(detail.getVendorId(), detail);
            } catch (Exception e) {
                log.error("Error in converting vendor cache for ID ; {}.  {}", data.getVendorId(), e.getMessage());
            }
        });
        return vendorDetails;
    }

}

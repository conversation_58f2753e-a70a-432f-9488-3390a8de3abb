package com.stpl.tech.scm.data.redis.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.scm.data.redis.RedisUtil;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.VendorDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.type.TypeReference;

@Component
public class RedisHashManager {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    public RedisHashService<Integer, ProductDefinition> productDefinition() {
        return new RedisHashService<>(redisTemplate, RedisUtil.REDIS_KEY_PRODUCT_DEFINITION, objectMapper, ProductDefinition.class, new TypeReference<>() {});
    }

    public RedisHashService<Integer, SkuDefinition> skuDefinition() {
        return new RedisHashService<>(redisTemplate, RedisUtil.REDIS_KEY_SKU_DEFINITION, objectMapper, SkuDefinition.class, new TypeReference<>() {});
    }

    public RedisHashService<Integer, VendorDetail> vendorDetail() {
        return new RedisHashService<>(redisTemplate, RedisUtil.REDIS_KEY_VENDOR_DETAIL, objectMapper, VendorDetail.class, new TypeReference<>() {});
    }
}


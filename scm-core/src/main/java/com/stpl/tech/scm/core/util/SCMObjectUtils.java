package com.stpl.tech.scm.core.util;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Slf4j
public class SCMObjectUtils {

    @SafeVarargs
    public static <T> T[] getArray(T... elements) {
        return elements;
    }

    @SafeVarargs
    public static <T> List<T> getList(T... elements) {
        return new ArrayList<>(Arrays.asList(elements));
    }

    @SafeVarargs
    public static <T> T firstNonNull(T... elements) {
        for (T element : elements) {
            if (element != null) {
                return element;
            }
        }
        return null;
    }

    public static int getSafeSize(Collection<?> collection) {
        return collection == null ? 0 : collection.size();
    }

}

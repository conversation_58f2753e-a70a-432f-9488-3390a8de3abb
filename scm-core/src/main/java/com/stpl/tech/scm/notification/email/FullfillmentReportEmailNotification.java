package com.stpl.tech.scm.notification.email;


import com.stpl.tech.scm.notification.email.template.FullfillmentReportEmailNotificationTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;


public class FullfillmentReportEmailNotification extends EmailNotification{

    FullfillmentReportEmailNotificationTemplate fullfillmentTemplate;

    EnvType envType;

    FullfillmentReportEmailNotification(){

    }
    public FullfillmentReportEmailNotification(FullfillmentReportEmailNotificationTemplate fullfillmentTemplate, EnvType envType){
            this.fullfillmentTemplate = fullfillmentTemplate;
            this.envType = envType;
    }

    @Override
    public String[] getToEmails() {
        if(envType==EnvType.PROD || envType == EnvType.SPROD){
            String[] emails = new String[2];
            emails[0]="<EMAIL>";
            emails[1] = "<EMAIL>";
            return emails;
        }else{
            String[] emails = new String[1];
            emails[0]="<EMAIL>";
            return emails;
        }

    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        if(envType==EnvType.PROD || envType==EnvType.SPROD) {
            return "SCM Report: Fullfilment Percentage (NEW) - Kitchen and Warehouse" + AppUtils.getCurrentDateISTFormatted();
        }else{
            return "[DEV] SCM Report: Fullfilment Percentage (NEW) - Kitchen and Warehouse" + AppUtils.getCurrentDateISTFormatted();
        }
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return fullfillmentTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

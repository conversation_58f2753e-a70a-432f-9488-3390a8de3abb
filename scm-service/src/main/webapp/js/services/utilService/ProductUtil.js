angular.module('scmApp').service('ProductUtil', [
    '$rootScope',
    'appUtil',
    'toast',
    'ScmApiService',
    'apiJson',
    function ($rootScope, appUtil, toast, ScmApiService, apiJson) {

        function handleResult(promise, callback) {
            var finalPromise = promise.then(function (data) {
                if (appUtil.checkEmpty(data)) {
                    toast.warning("Products not found");
                    if (callback) {
                        callback(null);
                    }
                    return null;
                }
                if (callback) {
                    callback(data);
                }
                return data;
            }).catch(function (error) {
                console.error("Error:", error);
                toast.error("Failed to fetch products data");
                if (callback) {
                    callback(null);
                }
                return null;
            });

            // Always return the promise so caller can .then() if they want
            return finalPromise;
        }

        this.getProductsBasicDetails = function (callback) {
            var promise = ScmApiService.get(apiJson.urls.productManagement.getProducts);
            return handleResult(promise, callback);
        };

        this.getByProductId = function (productId, callback) {
            var promise = ScmApiService.get(apiJson.urls.productManagement.productDetail, { productId: productId });
            return handleResult(promise, callback);
        };

    }
]);

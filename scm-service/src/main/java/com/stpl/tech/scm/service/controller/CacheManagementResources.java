package com.stpl.tech.scm.service.controller;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.stpl.tech.scm.core.service.MappingCache;
import com.stpl.tech.scm.domain.model.MilkBreadBypass;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.domain.model.AttributeDefinition;
import com.stpl.tech.scm.domain.model.AttributeValue;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.SCMUnitCategory;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VendorDetail;

/**
 * Created by Rahul Singh on 29-06-2016.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
    + SCMServiceConstants.CACHE_MANAGEMENT_ROOT_CONTEXT)
public class CacheManagementResources {

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private SCMMetadataService scmMetadataService;

    @Autowired
    private MappingCache mappingCache;

    @RequestMapping(method = RequestMethod.GET, value = "refresh-complete-cache")
    public boolean refreshAllCache() {
        scmCache.loadScmCache(true);
        return true;
    }

    @Scheduled(cron = "0 20 05 * * *", zone = "GMT+05:30")
    public void refreshCache() {
        scmCache.loadScmCache(true);
    }

    @Scheduled(cron = "0 30 04 * * *", zone = "GMT+05:30")
    public void createAssetCache() {
        scmCache.forceRefreshAssetCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-attribute-definition-cache")
    public void refreshAttributeDefinitionCache() {
        Map<Integer, AttributeDefinition> attributeDefinitions = new TreeMap<Integer, AttributeDefinition>();
        for(AttributeDefinition ad : scmMetadataService.getAllAttributeDefinitions()){
            attributeDefinitions.put(ad.getAttributeId(),ad);
        }
        scmCache.setAttributeDefinitions(attributeDefinitions);
    }


    @RequestMapping(method = RequestMethod.GET, value = "refresh-category-definition-cache")
    public void refreshCategoryDefinitionCache() {
        Map<Integer, CategoryDefinition> categoryDefinitions = new TreeMap<Integer, CategoryDefinition>();
        for (CategoryDefinition cd : scmMetadataService.getAllCategoryDefinitions()) {
            categoryDefinitions.put(cd.getCategoryId(), cd);
        }
        scmCache.setCategoryDefinitions(categoryDefinitions);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-packaging-definition-cache")
    public void refreshPackagingDefinitionCache() {
        Map<Integer, PackagingDefinition> packagingDefinitions = new TreeMap<Integer, PackagingDefinition>();
        for (PackagingDefinition pd : scmMetadataService.getAllPackagingDefinitions()) {
            packagingDefinitions.put(pd.getPackagingId(), pd);
        }
        scmCache.setPackagingDefinitions(packagingDefinitions);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-category-attribute-mapping-cache")
    public void refreshCategoryAttributeMappingCache() {
        Map<Integer, CategoryAttributeMapping> categoryAttributeMappings = new TreeMap<Integer, CategoryAttributeMapping>();
        for (CategoryAttributeMapping cam : scmMetadataService.getAllCategoryAttributeMappings()) {
            categoryAttributeMappings.put(cam.getCategoryAttributeMappingId(), cam);
        }
        scmCache.setCategoryAttributeMappings(categoryAttributeMappings);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-product-definition-cache")
    public void refreshProductDefinitionCache() {
        scmCache.forceRefreshProductCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-sku-definition-cache")
    public void refreshSkuDefinitionCache() {
        scmCache.forceRefreshSkuCache();
        refreshSkusByProductIdCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-vendor-detail-cache")
    public void refreshVendorDetailCache() {
        scmCache.forceRefreshVendorCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-unit-detail-cache")
    public void refreshUnitDetailCache() {
        Map<Integer, UnitDetail> unitDetails = new TreeMap<Integer, UnitDetail>();
        for (UnitDetail ud : scmMetadataService.getAllUnitDetails()) {
            unitDetails.put(ud.getUnitId(), ud);
        }
        scmCache.setUnitDetails(unitDetails);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-unit-category-cache")
    public void refreshUnitCategorieCache() {
        Map<Integer, SCMUnitCategory> unitCategories = new TreeMap<Integer, SCMUnitCategory>();
        for (SCMUnitCategory uc : scmMetadataService.getAllUnitCategories()) {
            unitCategories.put(uc.getCategoryId(), uc);
        }
        scmCache.setUnitCategories(unitCategories);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-attribute-value-cache")
    public void refreshAttributeValueCache() {
        Map<Integer, AttributeValue> attributeValues = new TreeMap<Integer, AttributeValue>();
        for (AttributeValue av : scmMetadataService.getAllAttributeValues()) {
            attributeValues.put(av.getAttributeValueId(), av);
        }
        scmCache.setAttributeValues(attributeValues);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-category-attribute-value-cache")
    public void refreshCategoryAttributeValueCache() {
        Map<Integer, CategoryAttributeValue> categoryAttributeValues = new TreeMap<Integer, CategoryAttributeValue>();
        for (CategoryAttributeValue cav : scmMetadataService.getAllCategoryAttributeValues()) {
            categoryAttributeValues.put(cav.getCategoryAttributeValueId(), cav);
        }
        scmCache.setCategoryAttributeValues(categoryAttributeValues);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-product-packaging-mapping-cache")
    public void refreshProductPackagingMappingCache() {
        Map<Integer, ProductPackagingMapping> productPackagingMappings = new TreeMap<Integer, ProductPackagingMapping>();
        for (ProductPackagingMapping ppm : scmMetadataService.getAllProductPackginMappings()) {
            productPackagingMappings.put(ppm.getProductPackagingMappingId(), ppm);
        }
        scmCache.setProductPackagingMappings(productPackagingMappings);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-category-packaging-mapping-cache")
    public void refreshSkuPackagingMappingCache() {
        Map<Integer, SkuPackagingMapping> skuPackagingMappings = new TreeMap<Integer, SkuPackagingMapping>();
        for (SkuPackagingMapping spm : scmMetadataService.getAllSkuPackagingMappings()) {
            skuPackagingMappings.put(spm.getSkuPackagingMappingId(), spm);
        }
        scmCache.setSkuPackagingMappings(skuPackagingMappings);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-sku-attribute-value-cache")
    public void refreshSkuAttributeValueCache() {
        Map<Integer, SkuAttributeValue> skuAttributeValues = new TreeMap<Integer, SkuAttributeValue>();
        for (SkuAttributeValue sav : scmMetadataService.getAllSkuAttributeValues()) {
            skuAttributeValues.put(sav.getSkuAttributeValueId(), sav);
        }
        scmCache.setSkuAttributeValues(skuAttributeValues);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-sub-category-definition-cache")
    public void refreshSubCategoryDefinitionCache() {
        Map<Integer, SubCategoryDefinition> subCategoryDefinitions = new TreeMap<Integer, SubCategoryDefinition>();
        for (SubCategoryDefinition scd : scmMetadataService.getAllSubCategoryDefinitions()) {
            subCategoryDefinitions.put(scd.getSubCategoryId(), scd);
        }
        scmCache.setSubCategoryDefinitions(subCategoryDefinitions);
    }

    @RequestMapping(method = RequestMethod.GET,value = "clear-production-line-cache")
    public void clearProductionLineCache(){
        mappingCache.removeAllProductionLine();
    }

    @RequestMapping(method = RequestMethod.GET, value = "clear-sku-definition-list-cache")
    public void clearSkuDefinitionListCache(){
        mappingCache.removeAllSkuDefinitionList();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-region-mapping-cache")
    public void refreshRegionMappingCache(){scmCache.refreshRegionFulfillmentMapping();}

    @RequestMapping(method = RequestMethod.GET, value = "refresh-pending-milk-bread-cache")
    public void refreshPendingMilkBreadCache(){scmCache.refreshPendingMilkBread();}

    @RequestMapping(method = RequestMethod.POST, value = "mark-milk-bread-complete")
    public void markMilkBreadComplete(@RequestBody List<Integer> unitIds, @RequestParam Integer userId) {
        scmMetadataService.markMilkBreadComplete(unitIds,userId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "mark-milk-bread-complete-for-unit")
    public void markMilkBreadCompleteForUnit(@RequestBody MilkBreadBypass milkBreadBypass) {
        scmMetadataService.markMilkBreadCompleteForUnit(milkBreadBypass);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-stock-take-app-version")
    public void refreshStockTakeAppVersion(){scmCache.refreshStockTakeAppVersion();}

    @GetMapping(value = "refresh-asset-cache")
    public void refreshAssetCache(){
        scmCache.forceRefreshAssetCache();
    }

    @GetMapping(value = "refresh-fulfillment-unit-mapping-cache")
    public void refreshFulfillmentUnitMapping() {
        scmCache.refreshFulfillmentMapping();
    }

    @GetMapping(value = "refresh-skus-by-productId")
    public void refreshSkusByProductIdCache() {
        scmCache.refreshSkusByProductIdCache();
    }

    @GetMapping(value = "get-cache")
    public <K, V> V getCacheByVariableName(@RequestParam String variableName, @RequestParam(required = false) Object id) {
        return scmCache.getCacheByVariableName(variableName, id);
    }

    @GetMapping("refresh-region-product-packaging")
    public void refreshRegionProductPackagingMapping() {
        scmCache.refreshRegionProductPackagingMapping();
    }

    @PostMapping("refresh-assets-by-units")
    public void refreshAssetsByUnits(@RequestBody List<Integer> unitIds) {
        scmMetadataService.saveAssetDefinitionToRedisByUnit(unitIds);
    }

}

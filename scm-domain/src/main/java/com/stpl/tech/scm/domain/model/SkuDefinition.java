//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.09 at 07:42:31 PM IST
//


package com.stpl.tech.scm.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.data.redis.core.index.Indexed;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for SkuDefinition complex type.
 * <p>
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;complexType name="SkuDefinition"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="skuDescription" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="supportsLooseOrdering" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="creationDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="hasInner" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="hasCase" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="linkedProduct" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="shelfLifeInDays" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="skuImage" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="negotiatedUnitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="priceLastUpdated" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="torqusSkuName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="isDefault" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="skuAttributes" type="{http://www.w3schools.com}SkuAttributeValue" maxOccurs="unbounded"/&gt;
 *         &lt;element name="skuPackagings" type="{http://www.w3schools.com}SkuPackagingMapping" maxOccurs="unbounded"/&gt;
 *         &lt;element name="skuCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SkuDefinition", propOrder = {
    "skuId",
    "skuName",
    "skuDescription",
    "supportsLooseOrdering",
    "creationDate",
    "createdBy",
    "hasInner",
    "hasCase",
    "linkedProduct",
    "shelfLifeInDays",
    "skuStatus",
    "unitOfMeasure",
    "skuImage",
    "unitPrice",
    "negotiatedUnitPrice",
    "priceLastUpdated",
    "torqusSkuName",
    "isDefault",
    "skuAttributes",
    "skuPackagings",
    "skuCode"
})
@RedisHash("SkuDefinition")
public class SkuDefinition {

    @Id
    @Indexed
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer skuId;
    @XmlElement(required = true)
    protected String skuName;
    @XmlElement(required = true)
    protected String skuDescription;
    protected boolean supportsLooseOrdering;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date creationDate;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    protected boolean hasInner;
    protected boolean hasCase;
    @XmlElement(required = true)
    protected IdCodeName linkedProduct;
    protected int shelfLifeInDays;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus skuStatus;
    @XmlElement(required = true)
    protected String unitOfMeasure;
    @XmlElement(required = true, nillable = true)
    protected String skuImage;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float unitPrice;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float negotiatedUnitPrice;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date priceLastUpdated;
    @XmlElement(required = true, nillable = true)
    protected String torqusSkuName;
    protected boolean isDefault;
    @XmlElement(required = true)
    protected List<SkuAttributeValue> skuAttributes;
    @XmlElement(required = true)
    protected List<SkuPackagingMapping> skuPackagings;
    private Integer inventoryList;
    @XmlElement(required = true)
    protected String skuCode;
    protected String taxCode;
    protected boolean isBranded;
    protected String brand;
    protected Integer approvalDocId;


    protected  List<EntityAttributeValueMapping> entityAttributeValueMappings;
    protected Date voDisContinuedFrom; //vo -> Vendor Ordering
    protected Date roDisContinuedFrom; //ro -> Request Order (adhoc)

    /**
     * Gets the value of the skuId property.
     *
     * @return
     *     possible object is
     * {@link Integer }
     *
     */
    public Integer getSkuId() {
        return skuId;
    }

    /**
     * Sets the value of the skuId property.
     *
     * @param value
     *     allowed object is
     *              {@link Integer }
     *
     */
    public void setSkuId(Integer value) {
        this.skuId = value;
    }

    /**
     * Gets the value of the skuName property.
     *
     * @return
     *     possible object is
     * {@link String }
     *
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     * Sets the value of the skuName property.
     *
     * @param value
     *     allowed object is
     *              {@link String }
     *
     */
    public void setSkuName(String value) {
        this.skuName = value;
    }

    /**
     * Gets the value of the skuDescription property.
     *
     * @return
     *     possible object is
     * {@link String }
     *
     */
    public String getSkuDescription() {
        return skuDescription;
    }

    /**
     * Sets the value of the skuDescription property.
     *
     * @param value
     *     allowed object is
     *              {@link String }
     *
     */
    public void setSkuDescription(String value) {
        this.skuDescription = value;
    }

    /**
     * Gets the value of the supportsLooseOrdering property.
     *
     */
    public boolean isSupportsLooseOrdering() {
        return supportsLooseOrdering;
    }

    /**
     * Sets the value of the supportsLooseOrdering property.
     *
     */
    public void setSupportsLooseOrdering(boolean value) {
        this.supportsLooseOrdering = value;
    }

    /**
     * Gets the value of the creationDate property.
     *
     * @return
     *     possible object is
     * {@link String }
     *
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * Sets the value of the creationDate property.
     *
     * @param value
     *     allowed object is
     *              {@link String }
     *
     */
    public void setCreationDate(Date value) {
        this.creationDate = value;
    }

    /**
     * Gets the value of the createdBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setCreatedBy(IdCodeName value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the hasInner property.
     *
     */
    public boolean isHasInner() {
        return hasInner;
    }

    /**
     * Sets the value of the hasInner property.
     *
     */
    public void setHasInner(boolean value) {
        this.hasInner = value;
    }

    /**
     * Gets the value of the hasCase property.
     *
     */
    public boolean isHasCase() {
        return hasCase;
    }

    /**
     * Sets the value of the hasCase property.
     *
     */
    public void setHasCase(boolean value) {
        this.hasCase = value;
    }

    /**
     * Gets the value of the linkedProduct property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getLinkedProduct() {
        return linkedProduct;
    }

    /**
     * Sets the value of the linkedProduct property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setLinkedProduct(IdCodeName value) {
        this.linkedProduct = value;
    }

    /**
     * Gets the value of the shelfLifeInDays property.
     *
     */
    public int getShelfLifeInDays() {
        return shelfLifeInDays;
    }

    /**
     * Sets the value of the shelfLifeInDays property.
     *
     */
    public void setShelfLifeInDays(int value) {
        this.shelfLifeInDays = value;
    }

    /**
     * Gets the value of the skuStatus property.
     *
     * @return
     *     possible object is
     * {@link SwitchStatus }
     *
     */
    public SwitchStatus getSkuStatus() {
        return skuStatus;
    }

    /**
     * Sets the value of the skuStatus property.
     *
     * @param value
     *     allowed object is
     *              {@link SwitchStatus }
     *
     */
    public void setSkuStatus(SwitchStatus value) {
        this.skuStatus = value;
    }

    /**
     * Gets the value of the unitOfMeasure property.
     *
     * @return
     *     possible object is
     * {@link String }
     *
     */
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    /**
     * Sets the value of the unitOfMeasure property.
     *
     * @param value
     *     allowed object is
     *              {@link String }
     *
     */
    public void setUnitOfMeasure(String value) {
        this.unitOfMeasure = value;
    }

    /**
     * Gets the value of the skuImage property.
     *
     * @return
     *     possible object is
     * {@link String }
     *
     */
    public String getSkuImage() {
        return skuImage;
    }

    /**
     * Sets the value of the skuImage property.
     *
     * @param value
     *     allowed object is
     *              {@link String }
     *
     */
    public void setSkuImage(String value) {
        this.skuImage = value;
    }

    /**
     * Gets the value of the unitPrice property.
     *
     * @return
     *     possible object is
     * {@link Float }
     *
     */
    public Float getUnitPrice() {
        return unitPrice;
    }

    /**
     * Sets the value of the unitPrice property.
     *
     * @param value
     *     allowed object is
     *              {@link Float }
     *
     */
    public void setUnitPrice(Float value) {
        this.unitPrice = value;
    }

    /**
     * Gets the value of the negotiatedUnitPrice property.
     *
     * @return
     *     possible object is
     * {@link Float }
     *
     */
    public Float getNegotiatedUnitPrice() {
        return negotiatedUnitPrice;
    }

    /**
     * Sets the value of the negotiatedUnitPrice property.
     *
     * @param value
     *     allowed object is
     *              {@link Float }
     *
     */
    public void setNegotiatedUnitPrice(Float value) {
        this.negotiatedUnitPrice = value;
    }

    /**
     * Gets the value of the priceLastUpdated property.
     *
     * @return
     *     possible object is
     * {@link String }
     *
     */
    public Date getPriceLastUpdated() {
        return priceLastUpdated;
    }

    /**
     * Sets the value of the priceLastUpdated property.
     *
     * @param value
     *     allowed object is
     *              {@link String }
     *
     */
    public void setPriceLastUpdated(Date value) {
        this.priceLastUpdated = value;
    }

    /**
     * Gets the value of the torqusSkuName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTorqusSkuName() {
        return torqusSkuName;
    }

    /**
     * Sets the value of the torqusSkuName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTorqusSkuName(String value) {
        this.torqusSkuName = value;
    }

    /**
     * Gets the value of the isDefault property.
     *
     */
    public boolean isIsDefault() {
        return isDefault;
    }

    /**
     * Sets the value of the isDefault property.
     *
     */
    public void setIsDefault(boolean value) {
        this.isDefault = value;
    }

    /**
     * Gets the value of the skuAttributes property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the skuAttributes property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSkuAttributes().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SkuAttributeValue }
     *
     *
     */
    public List<SkuAttributeValue> getSkuAttributes() {
        if (skuAttributes == null) {
            skuAttributes = new ArrayList<SkuAttributeValue>();
        }
        return this.skuAttributes;
    }

    public void setSkuAttributes(List<SkuAttributeValue> skuAttributes) {
        this.skuAttributes = skuAttributes;
    }

    /**
     * Gets the value of the skuPackagings property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the skuPackagings property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSkuPackagings().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SkuPackagingMapping }
     *
     *
     */
    public List<SkuPackagingMapping> getSkuPackagings() {
        if (skuPackagings == null) {
            skuPackagings = new ArrayList<SkuPackagingMapping>();
        }
        return this.skuPackagings;
    }

    public Integer getInventoryList() {
        return inventoryList;
    }

    public void setInventoryList(Integer inventoryList) {
        this.inventoryList = inventoryList;
    }

    /**
     * Gets the value of the skuCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     * Sets the value of the skuCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSkuCode(String value) {
        this.skuCode = value;
    }

    public List<EntityAttributeValueMapping> getEntityAttributeValueMappings() {
        return entityAttributeValueMappings;
    }

    public void setEntityAttributeValueMappings(List<EntityAttributeValueMapping> entityAttributeValueMappings) {
        this.entityAttributeValueMappings = entityAttributeValueMappings;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public Date getVoDisContinuedFrom() {
        return voDisContinuedFrom;
    }

    public void setVoDisContinuedFrom(Date voDisContinuedFrom) {
        this.voDisContinuedFrom = voDisContinuedFrom;
    }

    public Date getRoDisContinuedFrom() {
        return roDisContinuedFrom;
    }

    public void setRoDisContinuedFrom(Date roDisContinuedFrom) {
        this.roDisContinuedFrom = roDisContinuedFrom;
    }

    public boolean isIsBranded() {
        return isBranded;
    }
    public void setIsBranded(boolean branded) {
        isBranded = branded;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public Integer getApprovalDocId() {
        return approvalDocId;
    }

    public void setApprovalDocId(Integer approvalDocId) {
        this.approvalDocId = approvalDocId;
    }
}
